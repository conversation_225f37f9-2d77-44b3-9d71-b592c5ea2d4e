from datetime import datetime
from pathlib import Path
from typing import Tuple
from fastapi import <PERSON>TTPException, UploadFile
from fastapi.responses import FileResponse
import string
import random
import re


def generate_foto_id(length: int = 10) -> str:
    """Generate alpha-numeric string ID that doesn't start with 0"""
    chars = string.ascii_letters + string.digits
    first_char = random.choice(string.ascii_letters + "123456789")  # No 0 at start
    remaining_chars = "".join(random.choice(chars) for _ in range(length - 1))
    return first_char + remaining_chars


def generate_biodata_id(length: int = 6) -> str:
    """Generate 6-character alphanumeric ID for biodata generus"""
    chars = string.ascii_letters + string.digits
    # Ensure first character is not 0 for better readability
    first_char = random.choice(string.ascii_letters + "123456789")
    remaining_chars = "".join(random.choice(chars) for _ in range(length - 1))
    return first_char + remaining_chars


def sanitize_filename(text: str) -> str:
    """
    Sanitize text for use in filename by removing special characters,
    spaces, and converting to lowercase.

    Args:
        text: The text to sanitize

    Returns:
        Sanitized text safe for filesystem use
    """
    # Remove special characters and spaces, keep only alphanumeric and underscores
    sanitized = re.sub(r'[^a-zA-Z0-9_]', '', text.replace(' ', '_'))
    # Convert to lowercase
    sanitized = sanitized.lower()
    # Limit length to prevent overly long filenames
    return sanitized[:50] if sanitized else "unknown"


async def save_foto(
    foto: UploadFile, nama_id: str, nama_lengkap: str, tanggal: datetime
) -> Tuple[str, str]:
    """
    Save foto to disk and return foto_id and filename.

    Args:
        foto: The uploaded file
        nama_id: ID of the person (biodata_id)
        nama_lengkap: Full name of the person for filename
        tanggal: Date for the foto

    Returns:
        Tuple of (foto_id, filename)
    """
    # Validate file type (PNG only)
    if not foto.content_type or foto.content_type != "image/png":
        raise HTTPException(
            status_code=400,
            detail="Invalid file format. Only PNG files are allowed."
        )

    # Validate file extension from filename
    if foto.filename and not foto.filename.lower().endswith('.png'):
        raise HTTPException(
            status_code=400,
            detail="Invalid file extension. Only .png files are allowed."
        )

    # Create foto directory if it doesn't exist
    foto_dir = Path("fotos")
    foto_dir.mkdir(exist_ok=True)

    # Generate foto_id and create filename with enhanced naming convention
    foto_id = generate_foto_id()
    date_str = tanggal.strftime("%Y%m%d")
    sanitized_nama = sanitize_filename(nama_lengkap)

    # Format: {nama_id}_{sanitized_nama_lengkap}_{date_str}_{foto_id}.png
    filename = f"{nama_id}_{sanitized_nama}_{date_str}_{foto_id}.png"

    # Save file to disk
    file_path = foto_dir / filename
    try:
        with open(file_path, "wb") as buffer:
            content = await foto.read()
            buffer.write(content)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to save foto: {str(e)}")

    return foto_id, filename


def serve_foto(filename: str) -> FileResponse:
    """
    Serve foto file from disk by filename.

    Args:
        filename: Name of the foto file

    Returns:
        FileResponse with the image file
    """
    foto_dir = Path("fotos")
    file_path = foto_dir / filename

    if not file_path.exists():
        raise HTTPException(status_code=404, detail="Foto not found")

    # Determine media type based on file extension
    extension = filename.split(".")[-1].lower()
    media_type_map = {
        "jpg": "image/jpeg",
        "jpeg": "image/jpeg",
        "png": "image/png",
        "gif": "image/gif",
        "webp": "image/webp",
    }
    media_type = media_type_map.get(extension, "image/jpeg")

    return FileResponse(path=file_path, media_type=media_type, filename=filename)


def validate_foto_size(foto_content: bytes, max_size_kb: int = 2048) -> None:
    """
    Validate foto file size.

    Args:
        foto_content: The foto file content as bytes
        max_size_kb: Maximum allowed size in KB (default: 2048KB = 2MB)

    Raises:
        HTTPException: If foto exceeds size limit
    """
    if len(foto_content) > max_size_kb * 1024:
        raise HTTPException(
            status_code=422, detail=f"Foto size must be ≤{max_size_kb} kB (2MB)"
        )
